{"devDependencies": {"eslint": "^8.36.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-promise": "^6.1.1", "sequelize-cli": "^6.4.1"}, "scripts": {"start": "nodemon server.js"}, "dependencies": {"app-module-path": "^2.2.0", "bcrypt": "^5.0.1", "cors": "^2.8.5", "dotenv": "^16.0.1", "express": "^4.18.1", "fs": "^0.0.1-security", "http-status": "^1.5.2", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "moment-timezone": "^0.5.34", "nodemailer": "^6.7.7", "nodemon": "^2.0.19", "path": "^0.12.7", "pg": "^8.7.3", "sequelize": "^6.21.3", "swagger-ui-express": "^4.6.0", "uuid": "^8.3.2", "yamljs": "^0.3.0"}}