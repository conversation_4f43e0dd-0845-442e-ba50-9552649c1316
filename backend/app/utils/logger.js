const winston = require('winston');
const path = require('path');

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which logs to print based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Define which transports the logger must use
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }),
  
  // File transport for errors
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/error.log'),
    level: 'error',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  }),
  
  // File transport for all logs
  new winston.transports.File({
    filename: path.join(__dirname, '../../logs/combined.log'),
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    )
  })
];

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format,
  transports,
  exitOnError: false
});

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Add request logging middleware
logger.requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const message = `${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms`;
    
    if (res.statusCode >= 400) {
      logger.warn(message);
    } else {
      logger.info(message);
    }
  });
  
  next();
};

// Add transaction logging helpers
logger.transaction = {
  start: (transactionId) => {
    logger.info(`[TX:${transactionId}] Transaction started`);
  },
  
  commit: (transactionId) => {
    logger.info(`[TX:${transactionId}] Transaction committed successfully`);
  },
  
  rollback: (transactionId, error) => {
    logger.warn(`[TX:${transactionId}] Transaction rolled back due to error: ${error.message}`);
  },
  
  error: (transactionId, error) => {
    logger.error(`[TX:${transactionId}] Transaction failed:`, {
      error: error.message,
      stack: error.stack
    });
  }
};

// Add database query logging
logger.query = (sql, transactionId = null) => {
  const prefix = transactionId ? `[TX:${transactionId}] ` : '';
  logger.debug(`${prefix}SQL: ${sql}`);
};

// Add API logging helpers
logger.api = {
  request: (req) => {
    logger.info(`API Request: ${req.method} ${req.originalUrl}`, {
      body: req.body,
      query: req.query,
      params: req.params,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  },
  
  response: (req, res, data) => {
    logger.info(`API Response: ${req.method} ${req.originalUrl} ${res.statusCode}`, {
      data: typeof data === 'object' ? JSON.stringify(data) : data
    });
  },
  
  error: (req, error) => {
    logger.error(`API Error: ${req.method} ${req.originalUrl}`, {
      error: error.message,
      stack: error.stack,
      body: req.body,
      query: req.query,
      params: req.params
    });
  }
};

// Add authentication logging
logger.auth = {
  login: (email, success = true) => {
    if (success) {
      logger.info(`User logged in: ${email}`);
    } else {
      logger.warn(`Failed login attempt: ${email}`);
    }
  },
  
  logout: (email) => {
    logger.info(`User logged out: ${email}`);
  },
  
  register: (email) => {
    logger.info(`User registered: ${email}`);
  },
  
  tokenError: (error) => {
    logger.warn(`Token validation error: ${error.message}`);
  }
};

module.exports = logger;
