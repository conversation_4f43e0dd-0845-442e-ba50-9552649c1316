const { sequelize } = require('../models');
const logger = require('./logger');

/**
 * Execute a function within a database transaction
 * Automatically handles commit/rollback with detailed logging
 * 
 * @param {Function} callback - Function to execute within transaction
 * @param {Object} options - Transaction options
 * @returns {Promise} Result of the callback function
 */
async function withTransaction(callback, options = {}) {
  const transactionId = Math.random().toString(36).substr(2, 9);
  const transaction = await sequelize.transaction({
    ...options,
    logging: (sql) => logger.debug(`[TX:${transactionId}] ${sql}`)
  });
  
  logger.info(`[TX:${transactionId}] Transaction started`);
  
  try {
    const result = await callback(transaction);
    
    // Ensure transaction is still active before committing
    if (transaction.finished) {
      logger.warn(`[TX:${transactionId}] Transaction already finished, skipping commit`);
      return result;
    }
    
    await transaction.commit();
    logger.info(`[TX:${transactionId}] Transaction committed successfully`);
    return result;
    
  } catch (error) {
    // Ensure transaction is still active before rolling back
    if (!transaction.finished) {
      try {
        await transaction.rollback();
        logger.warn(`[TX:${transactionId}] Transaction rolled back due to error: ${error.message}`);
      } catch (rollbackError) {
        logger.error(`[TX:${transactionId}] Failed to rollback transaction:`, rollbackError);
      }
    } else {
      logger.warn(`[TX:${transactionId}] Transaction already finished, cannot rollback`);
    }
    
    logger.error(`[TX:${transactionId}] Transaction failed:`, {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

/**
 * Execute multiple operations in a single transaction
 * 
 * @param {Array} operations - Array of functions that accept transaction as parameter
 * @param {Object} options - Transaction options
 * @returns {Promise<Array>} Array of results from each operation
 */
async function withBatchTransaction(operations, options = {}) {
  return withTransaction(async (transaction) => {
    const results = [];
    
    for (const operation of operations) {
      const result = await operation(transaction);
      results.push(result);
    }
    
    return results;
  }, options);
}

/**
 * Retry a transaction operation with exponential backoff
 * Useful for handling deadlocks and temporary failures
 * 
 * @param {Function} callback - Function to execute within transaction
 * @param {Object} options - Options including maxRetries, baseDelay
 * @returns {Promise} Result of the callback function
 */
async function withRetryTransaction(callback, options = {}) {
  const {
    maxRetries = 3,
    baseDelay = 100,
    maxDelay = 5000,
    retryCondition = (error) => {
      // Retry on deadlocks, connection errors, and timeout errors
      return error.name === 'SequelizeDeadlockError' ||
             error.name === 'SequelizeConnectionError' ||
             error.name === 'SequelizeTimeoutError';
    }
  } = options;

  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await withTransaction(callback, options);
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries || !retryCondition(error)) {
        throw error;
      }
      
      // Calculate delay with exponential backoff and jitter
      const delay = Math.min(
        baseDelay * Math.pow(2, attempt) + Math.random() * 100,
        maxDelay
      );
      
      logger.warn(`Transaction attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Create a transaction-aware version of a function
 * The returned function will automatically wrap the original in a transaction
 * 
 * @param {Function} fn - Function to wrap
 * @param {Object} options - Transaction options
 * @returns {Function} Transaction-wrapped function
 */
function transactional(fn, options = {}) {
  return async function(...args) {
    return withTransaction(async (transaction) => {
      return fn.call(this, ...args, transaction);
    }, options);
  };
}

/**
 * Check if we're currently in a transaction
 * 
 * @param {Object} transaction - Transaction object to check
 * @returns {boolean} True if in transaction
 */
function isInTransaction(transaction) {
  return transaction && typeof transaction.commit === 'function';
}

/**
 * Execute operation with existing transaction or create new one
 * 
 * @param {Function} callback - Function to execute
 * @param {Object} transaction - Existing transaction (optional)
 * @param {Object} options - Transaction options
 * @returns {Promise} Result of the callback function
 */
async function withOptionalTransaction(callback, transaction = null, options = {}) {
  if (isInTransaction(transaction)) {
    // Use existing transaction
    return callback(transaction);
  } else {
    // Create new transaction
    return withTransaction(callback, options);
  }
}

module.exports = {
  withTransaction,
  withBatchTransaction,
  withRetryTransaction,
  transactional,
  isInTransaction,
  withOptionalTransaction
};
