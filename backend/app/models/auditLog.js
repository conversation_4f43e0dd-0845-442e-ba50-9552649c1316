const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const AuditLog = sequelize.define('AuditLog', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => uuidv4()
    },
    type: {
      type: DataTypes.ENUM(
        'USER_CREATED',
        'USER_UPDATED',
        'USER_DELETED',
        'LOGIN_SUCCESS',
        'LOGIN_FAILED',
        'LOGOUT',
        'PASSWORD_CHANGED',
        'EMAIL_VERIFIED',
        'ORGANIZATION_CREATED',
        'OR<PERSON><PERSON>ZATION_UPDATED',
        'ORGANIZATION_VERIFIED',
        'PROJECT_CREATED',
        'PROJECT_UPDATED',
        'PROJECT_VERIFIED',
        'CREDIT_CREATED',
        'CREDIT_UPDATED',
        'CREDIT_PURCHASED',
        'CREDIT_RETIRED',
        'ORDER_CREATED',
        'ORDER_COMPLETED',
        'ORDER_CANCELLED',
        'PAYMENT_PROCESSED',
        'PAYMENT_FAILED',
        'TRANSACTION_CREATED',
        'WALLET_CREATED',
        'WALLET_UPDATED',
        'NOTIFICATION_SENT',
        'SYSTEM_ERROR',
        'DATA_EXPORT',
        'DATA_IMPORT',
        'ADMIN_ACTION',
        'OTHER'
      ),
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    details: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    },
    ipAddress: {
      type: DataTypes.STRING,
      allowNull: true
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    severity: {
      type: DataTypes.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'),
      allowNull: false,
      defaultValue: 'LOW'
    },
    category: {
      type: DataTypes.ENUM('AUTHENTICATION', 'AUTHORIZATION', 'DATA', 'SYSTEM', 'BUSINESS', 'SECURITY'),
      allowNull: false,
      defaultValue: 'BUSINESS'
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Auth',
        key: 'id'
      }
    },
    organizationId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id'
      }
    },
    projectId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Project',
        key: 'id'
      }
    },
    carbonCreditId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'CarbonCredit',
        key: 'id'
      }
    },
    orderId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Order',
        key: 'id'
      }
    }
  }, {
    tableName: 'AuditLog',
    timestamps: true,
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['severity']
      },
      {
        fields: ['category']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['organizationId']
      },
      {
        fields: ['createdAt']
      },
      {
        fields: ['ipAddress']
      }
    ]
  });

  // Instance methods
  AuditLog.prototype.isCritical = function() {
    return this.severity === 'CRITICAL';
  };

  AuditLog.prototype.isSecurityRelated = function() {
    return this.category === 'SECURITY' || 
           ['LOGIN_FAILED', 'PASSWORD_CHANGED', 'UNAUTHORIZED_ACCESS'].includes(this.type);
  };

  AuditLog.prototype.addDetail = function(key, value) {
    const details = this.details || {};
    details[key] = value;
    return this.update({ details });
  };

  // Class methods
  AuditLog.logEvent = async function(type, description, details = {}, userId = null, organizationId = null, ipAddress = null, userAgent = null, options = {}) {
    const severity = options.severity || 'LOW';
    const category = options.category || 'BUSINESS';
    
    return this.create({
      type,
      description,
      details,
      userId,
      organizationId,
      ipAddress,
      userAgent,
      severity,
      category,
      projectId: options.projectId || null,
      carbonCreditId: options.carbonCreditId || null,
      orderId: options.orderId || null
    });
  };

  AuditLog.findByUser = function(userId, limit = 100) {
    return this.findAll({
      where: { userId },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findByOrganization = function(organizationId, limit = 100) {
    return this.findAll({
      where: { organizationId },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findByType = function(type, limit = 100) {
    return this.findAll({
      where: { type },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findBySeverity = function(severity, limit = 100) {
    return this.findAll({
      where: { severity },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findSecurityEvents = function(limit = 100) {
    return this.findAll({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { category: 'SECURITY' },
          { 
            type: {
              [sequelize.Sequelize.Op.in]: [
                'LOGIN_FAILED', 
                'PASSWORD_CHANGED', 
                'UNAUTHORIZED_ACCESS',
                'ADMIN_ACTION'
              ]
            }
          }
        ]
      },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findByDateRange = function(startDate, endDate, limit = 1000) {
    return this.findAll({
      where: {
        createdAt: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.findRecentActivity = function(hours = 24, limit = 100) {
    const since = new Date(Date.now() - (hours * 60 * 60 * 1000));
    return this.findAll({
      where: {
        createdAt: {
          [sequelize.Sequelize.Op.gte]: since
        }
      },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  AuditLog.getStatsByType = function(startDate = null, endDate = null) {
    const where = {};
    if (startDate && endDate) {
      where.createdAt = {
        [sequelize.Sequelize.Op.between]: [startDate, endDate]
      };
    }

    return this.findAll({
      where,
      attributes: [
        'type',
        [sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'count']
      ],
      group: ['type'],
      order: [[sequelize.Sequelize.fn('COUNT', sequelize.Sequelize.col('id')), 'DESC']]
    });
  };

  // Associations
  AuditLog.associate = function(models) {
    // AuditLog belongs to user
    AuditLog.belongsTo(models.Auth, {
      foreignKey: 'userId',
      as: 'user'
    });

    // AuditLog belongs to organization
    AuditLog.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization'
    });

    // AuditLog belongs to project
    if (models.Project) {
      AuditLog.belongsTo(models.Project, {
        foreignKey: 'projectId',
        as: 'project'
      });
    }

    // AuditLog belongs to carbon credit
    if (models.CarbonCredit) {
      AuditLog.belongsTo(models.CarbonCredit, {
        foreignKey: 'carbonCreditId',
        as: 'carbonCredit'
      });
    }

    // AuditLog belongs to order
    if (models.Order) {
      AuditLog.belongsTo(models.Order, {
        foreignKey: 'orderId',
        as: 'order'
      });
    }
  };

  return AuditLog;
};
