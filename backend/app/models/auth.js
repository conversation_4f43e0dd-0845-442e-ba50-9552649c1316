const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { USER_STATUS } = require('../constant/auth');

module.exports = (sequelize, DataTypes) => {
  const Auth = sequelize.define(
    'Auth',
    {
      id: {
        type: DataTypes.STRING,
        primaryKey: true,
        defaultValue: () => uuidv4()
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true
        }
      },
      name: {
        type: DataTypes.STRING,
        allowNull: true
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true
      },
      role: {
        type: DataTypes.ENUM('ADMIN', 'ORGANIZATION_ADMIN', 'USER'),
        allowNull: false,
        defaultValue: 'USER'
      },
      emailVerified: {
        type: DataTypes.DATE,
        allowNull: true
      },
      jobTitle: {
        type: DataTypes.STRING,
        allowNull: true
      },
      departmentName: {
        type: DataTypes.STRING,
        allowNull: true
      },
      phoneNumber: {
        type: DataTypes.STRING,
        allowNull: true
      },
      profileImage: {
        type: DataTypes.STRING,
        allowNull: true
      },
      bio: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      twoFactorEnabled: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      preferences: {
        type: DataTypes.JSONB,
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED', 'PENDING'),
        allowNull: false,
        defaultValue: 'ACTIVE'
      },
      organizationId: {
        type: DataTypes.STRING,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id'
        }
      }
    },
    {
      tableName: 'Auth',
      timestamps: true,
      hooks: {
        beforeCreate: async (user) => {
          if (user.password) {
            user.password = await bcrypt.hash(user.password, 12);
          }
        },
        beforeUpdate: async (user) => {
          if (user.changed('password') && user.password) {
            user.password = await bcrypt.hash(user.password, 12);
          }
        }
      },
      indexes: [
        {
          fields: ['email'],
          unique: true
        },
        {
          fields: ['role']
        },
        {
          fields: ['organizationId']
        },
        {
          fields: ['status']
        }
      ]
    }
  );

  // Instance methods
  Auth.prototype.validatePassword = async function(password) {
    if (!this.password) return false;
    return bcrypt.compare(password, this.password);
  };

  Auth.prototype.isAdmin = function() {
    return this.role === 'ADMIN';
  };

  Auth.prototype.isOrganizationAdmin = function() {
    return this.role === 'ORGANIZATION_ADMIN';
  };

  Auth.prototype.isActive = function() {
    return this.status === 'ACTIVE';
  };

  Auth.prototype.isEmailVerified = function() {
    return this.emailVerified !== null;
  };

  Auth.prototype.updateLastLogin = function() {
    return this.update({ lastLoginAt: new Date() });
  };

  Auth.prototype.toJSON = function() {
    const values = Object.assign({}, this.get());
    delete values.password;
    return values;
  };

  Auth.associate = (models) => {
    // Existing session association
    Auth.hasOne(models.Session, { foreignKey: 'auth_id' });

    // New associations for carbon credit system
    Auth.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization'
    });

    // User has many projects
    if (models.Project) {
      Auth.hasMany(models.Project, {
        foreignKey: 'userId',
        as: 'projects'
      });
    }

    // User has many carbon credits
    if (models.CarbonCredit) {
      Auth.hasMany(models.CarbonCredit, {
        foreignKey: 'userId',
        as: 'carbonCredits'
      });
    }

    // User has many orders as buyer
    if (models.Order) {
      Auth.hasMany(models.Order, {
        foreignKey: 'buyerId',
        as: 'purchaseOrders'
      });

      // User has many orders as seller
      Auth.hasMany(models.Order, {
        foreignKey: 'sellerId',
        as: 'salesOrders'
      });
    }

    // User has many audit logs
    if (models.AuditLog) {
      Auth.hasMany(models.AuditLog, {
        foreignKey: 'userId',
        as: 'auditLogs'
      });
    }

    // User has many notifications
    if (models.Notification) {
      Auth.hasMany(models.Notification, {
        foreignKey: 'userId',
        as: 'notifications'
      });
    }

    // User has many wallets
    if (models.Wallet) {
      Auth.hasMany(models.Wallet, {
        foreignKey: 'userId',
        as: 'wallets'
      });
    }
  };

  return Auth;
};
