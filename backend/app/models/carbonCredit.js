const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const CarbonCredit = sequelize.define('CarbonCredit', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => uuidv4()
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    availableQuantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    vintage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1990,
        max: new Date().getFullYear() + 10
      }
    },
    standard: {
      type: DataTypes.ENUM('VCS', 'CDM', 'GOLD_STANDARD', 'CAR', 'ACR', 'OTHER'),
      allowNull: false
    },
    methodology: {
      type: DataTypes.STRING,
      allowNull: false
    },
    serialNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true
    },
    batchNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'ACTIVE', 'SOLD', 'RETIRED', 'CANCELLED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    verificationStatus: {
      type: DataTypes.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    listingDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    expiryDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    retirementDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    retirementReason: {
      type: DataTypes.STRING,
      allowNull: true
    },
    retirementBeneficiary: {
      type: DataTypes.STRING,
      allowNull: true
    },
    minPurchaseQuantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1
      }
    },
    maxPurchaseQuantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1
      }
    },
    additionalData: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    },
    projectId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Project',
        key: 'id'
      }
    },
    organizationId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Organization',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Auth',
        key: 'id'
      }
    }
  }, {
    tableName: 'CarbonCredit',
    timestamps: true,
    indexes: [
      {
        fields: ['status']
      },
      {
        fields: ['verificationStatus']
      },
      {
        fields: ['vintage']
      },
      {
        fields: ['standard']
      },
      {
        fields: ['price']
      },
      {
        fields: ['projectId']
      },
      {
        fields: ['organizationId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['serialNumber'],
        unique: true
      }
    ]
  });

  // Instance methods
  CarbonCredit.prototype.isActive = function() {
    return this.status === 'ACTIVE';
  };

  CarbonCredit.prototype.isVerified = function() {
    return this.verificationStatus === 'VERIFIED';
  };

  CarbonCredit.prototype.isAvailable = function() {
    return this.isActive() && this.isVerified() && this.availableQuantity > 0;
  };

  CarbonCredit.prototype.isRetired = function() {
    return this.status === 'RETIRED';
  };

  CarbonCredit.prototype.canPurchase = function(quantity) {
    if (!this.isAvailable()) {
      return false;
    }
    
    if (quantity < this.minPurchaseQuantity) {
      return false;
    }
    
    if (this.maxPurchaseQuantity && quantity > this.maxPurchaseQuantity) {
      return false;
    }
    
    return quantity <= this.availableQuantity;
  };

  CarbonCredit.prototype.calculateTotalPrice = function(quantity) {
    return parseFloat(this.price) * quantity;
  };

  CarbonCredit.prototype.retire = function(reason, beneficiary) {
    return this.update({
      status: 'RETIRED',
      retirementDate: new Date(),
      retirementReason: reason,
      retirementBeneficiary: beneficiary,
      availableQuantity: 0
    });
  };

  CarbonCredit.prototype.updateAvailableQuantity = function(soldQuantity) {
    const newAvailableQuantity = this.availableQuantity - soldQuantity;
    const newStatus = newAvailableQuantity === 0 ? 'SOLD' : this.status;
    
    return this.update({
      availableQuantity: newAvailableQuantity,
      status: newStatus
    });
  };

  CarbonCredit.prototype.generateSerialNumber = function() {
    if (this.serialNumber) {
      return this.serialNumber;
    }
    
    const prefix = this.standard.substring(0, 3);
    const vintage = this.vintage.toString();
    const random = Math.random().toString(36).substr(2, 8).toUpperCase();
    const serialNumber = `${prefix}-${vintage}-${random}`;
    
    return this.update({ serialNumber });
  };

  // Class methods
  CarbonCredit.findAvailable = function() {
    return this.findAll({
      where: {
        status: 'ACTIVE',
        verificationStatus: 'VERIFIED',
        availableQuantity: {
          [sequelize.Sequelize.Op.gt]: 0
        }
      }
    });
  };

  CarbonCredit.findByVintage = function(vintage) {
    return this.findAll({
      where: {
        vintage
      }
    });
  };

  CarbonCredit.findByStandard = function(standard) {
    return this.findAll({
      where: {
        standard
      }
    });
  };

  CarbonCredit.findByPriceRange = function(minPrice, maxPrice) {
    const where = {};
    if (minPrice !== undefined) {
      where.price = { [sequelize.Sequelize.Op.gte]: minPrice };
    }
    if (maxPrice !== undefined) {
      where.price = { ...where.price, [sequelize.Sequelize.Op.lte]: maxPrice };
    }
    
    return this.findAll({ where });
  };

  // Associations
  CarbonCredit.associate = function(models) {
    // CarbonCredit belongs to project
    CarbonCredit.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project'
    });

    // CarbonCredit belongs to organization
    CarbonCredit.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization'
    });

    // CarbonCredit belongs to user
    CarbonCredit.belongsTo(models.Auth, {
      foreignKey: 'userId',
      as: 'user'
    });

    // CarbonCredit has many orders
    if (models.Order) {
      CarbonCredit.hasMany(models.Order, {
        foreignKey: 'carbonCreditId',
        as: 'orders'
      });
    }

    // CarbonCredit has many transactions
    if (models.Transaction) {
      CarbonCredit.hasMany(models.Transaction, {
        foreignKey: 'carbonCreditId',
        as: 'transactions'
      });
    }

    // CarbonCredit has many audit logs
    if (models.AuditLog) {
      CarbonCredit.hasMany(models.AuditLog, {
        foreignKey: 'carbonCreditId',
        as: 'auditLogs'
      });
    }
  };

  return CarbonCredit;
};
