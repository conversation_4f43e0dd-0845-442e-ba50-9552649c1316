const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const Order = sequelize.define('Order', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => uuidv4()
    },
    orderNumber: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    type: {
      type: DataTypes.ENUM('BUY', 'SELL'),
      allowNull: false
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1
      }
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    totalAmount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'CONFIRMED', 'PROCESSING', 'COMPLETED', 'CANCELLED', 'FAILED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    paymentStatus: {
      type: DataTypes.ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'REFUNDED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    paymentMethod: {
      type: DataTypes.ENUM('CREDIT_CARD', 'BANK_TRANSFER', 'CRYPTO', 'WALLET', 'OTHER'),
      allowNull: true
    },
    paymentReference: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionFee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00
    },
    platformFee: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    },
    completedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    cancelledAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    cancellationReason: {
      type: DataTypes.STRING,
      allowNull: true
    },
    carbonCreditId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'CarbonCredit',
        key: 'id'
      }
    },
    buyerId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Auth',
        key: 'id'
      }
    },
    sellerId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Auth',
        key: 'id'
      }
    },
    buyerOrganizationId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id'
      }
    },
    sellerOrganizationId: {
      type: DataTypes.STRING,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id'
      }
    }
  }, {
    tableName: 'Order',
    timestamps: true,
    indexes: [
      {
        fields: ['orderNumber'],
        unique: true
      },
      {
        fields: ['status']
      },
      {
        fields: ['paymentStatus']
      },
      {
        fields: ['type']
      },
      {
        fields: ['carbonCreditId']
      },
      {
        fields: ['buyerId']
      },
      {
        fields: ['sellerId']
      },
      {
        fields: ['buyerOrganizationId']
      },
      {
        fields: ['sellerOrganizationId']
      }
    ],
    hooks: {
      beforeCreate: (order) => {
        // Generate order number if not provided
        if (!order.orderNumber) {
          const timestamp = Date.now().toString();
          const random = Math.random().toString(36).substr(2, 4).toUpperCase();
          order.orderNumber = `ORD-${timestamp}-${random}`;
        }
        
        // Calculate total amount
        order.totalAmount = parseFloat(order.price) * order.quantity + 
                           parseFloat(order.transactionFee || 0) + 
                           parseFloat(order.platformFee || 0);
      },
      beforeUpdate: (order) => {
        // Recalculate total amount if price, quantity, or fees change
        if (order.changed('price') || order.changed('quantity') || 
            order.changed('transactionFee') || order.changed('platformFee')) {
          order.totalAmount = parseFloat(order.price) * order.quantity + 
                             parseFloat(order.transactionFee || 0) + 
                             parseFloat(order.platformFee || 0);
        }
      }
    }
  });

  // Instance methods
  Order.prototype.isPending = function() {
    return this.status === 'PENDING';
  };

  Order.prototype.isCompleted = function() {
    return this.status === 'COMPLETED';
  };

  Order.prototype.isCancelled = function() {
    return this.status === 'CANCELLED';
  };

  Order.prototype.canCancel = function() {
    return ['PENDING', 'CONFIRMED'].includes(this.status);
  };

  Order.prototype.canComplete = function() {
    return this.status === 'PROCESSING' && this.paymentStatus === 'COMPLETED';
  };

  Order.prototype.complete = function() {
    return this.update({
      status: 'COMPLETED',
      completedAt: new Date()
    });
  };

  Order.prototype.cancel = function(reason) {
    return this.update({
      status: 'CANCELLED',
      cancelledAt: new Date(),
      cancellationReason: reason
    });
  };

  Order.prototype.updatePaymentStatus = function(status, reference = null) {
    const updateData = { paymentStatus: status };
    if (reference) {
      updateData.paymentReference = reference;
    }
    return this.update(updateData);
  };

  Order.prototype.calculateFees = function(transactionFeeRate = 0.01, platformFeeRate = 0.005) {
    const subtotal = parseFloat(this.price) * this.quantity;
    const transactionFee = subtotal * transactionFeeRate;
    const platformFee = subtotal * platformFeeRate;
    
    return this.update({
      transactionFee,
      platformFee,
      totalAmount: subtotal + transactionFee + platformFee
    });
  };

  Order.prototype.getSubtotal = function() {
    return parseFloat(this.price) * this.quantity;
  };

  Order.prototype.getTotalFees = function() {
    return parseFloat(this.transactionFee || 0) + parseFloat(this.platformFee || 0);
  };

  // Class methods
  Order.findPendingOrders = function() {
    return this.findAll({
      where: {
        status: 'PENDING'
      }
    });
  };

  Order.findCompletedOrders = function() {
    return this.findAll({
      where: {
        status: 'COMPLETED'
      }
    });
  };

  Order.findByBuyer = function(buyerId) {
    return this.findAll({
      where: {
        buyerId
      }
    });
  };

  Order.findBySeller = function(sellerId) {
    return this.findAll({
      where: {
        sellerId
      }
    });
  };

  Order.findByDateRange = function(startDate, endDate) {
    return this.findAll({
      where: {
        createdAt: {
          [sequelize.Sequelize.Op.between]: [startDate, endDate]
        }
      }
    });
  };

  // Associations
  Order.associate = function(models) {
    // Order belongs to carbon credit
    Order.belongsTo(models.CarbonCredit, {
      foreignKey: 'carbonCreditId',
      as: 'carbonCredit'
    });

    // Order belongs to buyer (user)
    Order.belongsTo(models.Auth, {
      foreignKey: 'buyerId',
      as: 'buyer'
    });

    // Order belongs to seller (user)
    Order.belongsTo(models.Auth, {
      foreignKey: 'sellerId',
      as: 'seller'
    });

    // Order belongs to buyer organization
    Order.belongsTo(models.Organization, {
      foreignKey: 'buyerOrganizationId',
      as: 'buyerOrganization'
    });

    // Order belongs to seller organization
    Order.belongsTo(models.Organization, {
      foreignKey: 'sellerOrganizationId',
      as: 'sellerOrganization'
    });

    // Order has many transactions
    if (models.Transaction) {
      Order.hasMany(models.Transaction, {
        foreignKey: 'orderId',
        as: 'transactions'
      });
    }

    // Order has many audit logs
    if (models.AuditLog) {
      Order.hasMany(models.AuditLog, {
        foreignKey: 'orderId',
        as: 'auditLogs'
      });
    }

    // Order has many notifications
    if (models.Notification) {
      Order.hasMany(models.Notification, {
        foreignKey: 'orderId',
        as: 'notifications'
      });
    }
  };

  return Order;
};
