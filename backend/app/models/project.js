const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const Project = sequelize.define('Project', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => uuidv4()
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    type: {
      type: DataTypes.ENUM(
        'RENEWABLE_ENERGY',
        'FOREST_CONSERVATION',
        'REFORESTATION',
        'ENERGY_EFFICIENCY',
        'METHANE_CAPTURE',
        'CARBON_CAPTURE',
        'SUSTAINABLE_AGRICULTURE',
        'WASTE_MANAGEMENT',
        'TRANSPORTATION',
        'OTHER'
      ),
      allowNull: false
    },
    methodology: {
      type: DataTypes.STRING,
      allowNull: false
    },
    standard: {
      type: DataTypes.ENUM('VCS', 'CDM', 'GOLD_STANDARD', 'CAR', 'ACR', 'OTHER'),
      allowNull: false
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false
    },
    country: {
      type: DataTypes.STRING,
      allowNull: false
    },
    coordinates: {
      type: DataTypes.GEOMETRY('POINT'),
      allowNull: true
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('PLANNING', 'ACTIVE', 'COMPLETED', 'SUSPENDED', 'CANCELLED'),
      allowNull: false,
      defaultValue: 'PLANNING'
    },
    verificationStatus: {
      type: DataTypes.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    totalCreditsPotential: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0
      }
    },
    creditsIssued: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    creditsRetired: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    vintage: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1990,
        max: new Date().getFullYear() + 10
      }
    },
    registryId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    registryUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    documents: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: []
    },
    images: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: []
    },
    additionalData: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {}
    },
    organizationId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Organization',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'Auth',
        key: 'id'
      }
    }
  }, {
    tableName: 'Project',
    timestamps: true,
    indexes: [
      {
        fields: ['type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['verificationStatus']
      },
      {
        fields: ['country']
      },
      {
        fields: ['vintage']
      },
      {
        fields: ['organizationId']
      },
      {
        fields: ['userId']
      }
    ]
  });

  // Instance methods
  Project.prototype.isActive = function() {
    return this.status === 'ACTIVE';
  };

  Project.prototype.isVerified = function() {
    return this.verificationStatus === 'VERIFIED';
  };

  Project.prototype.canIssueCredits = function() {
    return this.isActive() && this.isVerified();
  };

  Project.prototype.getAvailableCredits = function() {
    return this.creditsIssued - this.creditsRetired;
  };

  Project.prototype.getCompletionPercentage = function() {
    if (!this.totalCreditsPotential || this.totalCreditsPotential === 0) {
      return 0;
    }
    return Math.min((this.creditsIssued / this.totalCreditsPotential) * 100, 100);
  };

  Project.prototype.addDocument = function(document) {
    const documents = this.documents || [];
    documents.push({
      ...document,
      uploadedAt: new Date(),
      id: uuidv4()
    });
    return this.update({ documents });
  };

  Project.prototype.addImage = function(image) {
    const images = this.images || [];
    images.push({
      ...image,
      uploadedAt: new Date(),
      id: uuidv4()
    });
    return this.update({ images });
  };

  // Class methods
  Project.findActiveProjects = function() {
    return this.findAll({
      where: {
        status: 'ACTIVE'
      }
    });
  };

  Project.findVerifiedProjects = function() {
    return this.findAll({
      where: {
        verificationStatus: 'VERIFIED'
      }
    });
  };

  Project.findByType = function(type) {
    return this.findAll({
      where: {
        type
      }
    });
  };

  Project.findByCountry = function(country) {
    return this.findAll({
      where: {
        country
      }
    });
  };

  // Associations
  Project.associate = function(models) {
    // Project belongs to organization
    Project.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization'
    });

    // Project belongs to user
    Project.belongsTo(models.Auth, {
      foreignKey: 'userId',
      as: 'user'
    });

    // Project has many carbon credits
    if (models.CarbonCredit) {
      Project.hasMany(models.CarbonCredit, {
        foreignKey: 'projectId',
        as: 'carbonCredits'
      });
    }

    // Project has many audit logs
    if (models.AuditLog) {
      Project.hasMany(models.AuditLog, {
        foreignKey: 'projectId',
        as: 'auditLogs'
      });
    }
  };

  return Project;
};
