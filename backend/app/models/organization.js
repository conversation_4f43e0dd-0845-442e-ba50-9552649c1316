const { v4: uuidv4 } = require('uuid');

module.exports = (sequelize, DataTypes) => {
  const Organization = sequelize.define('Organization', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      defaultValue: () => uuidv4()
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 200]
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    logo: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM('PENDING', 'ACTIVE', 'SUSPENDED', 'INACTIVE'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    verificationStatus: {
      type: DataTypes.ENUM('PENDING', 'IN_REVIEW', 'VERIFIED', 'REJECTED'),
      allowNull: false,
      defaultValue: 'PENDING'
    },
    legalName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    registrationNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    taxId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true
    },
    postalCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    industry: {
      type: DataTypes.STRING,
      allowNull: true
    },
    size: {
      type: DataTypes.ENUM('SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'),
      allowNull: true
    },
    foundedYear: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1800,
        max: new Date().getFullYear()
      }
    },
    primaryContact: {
      type: DataTypes.STRING,
      allowNull: true
    },
    primaryContactEmail: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true
      }
    },
    primaryContactPhone: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionFeeRate: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.01,
      validate: {
        min: 0,
        max: 1
      }
    },
    listingFeeRate: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.005,
      validate: {
        min: 0,
        max: 1
      }
    },
    subscriptionFee: {
      type: DataTypes.FLOAT,
      allowNull: false,
      defaultValue: 0.0,
      validate: {
        min: 0
      }
    }
  }, {
    tableName: 'Organization',
    timestamps: true,
    indexes: [
      {
        fields: ['status']
      },
      {
        fields: ['verificationStatus']
      },
      {
        fields: ['country']
      },
      {
        fields: ['industry']
      }
    ]
  });

  // Instance methods
  Organization.prototype.isActive = function() {
    return this.status === 'ACTIVE';
  };

  Organization.prototype.isVerified = function() {
    return this.verificationStatus === 'VERIFIED';
  };

  Organization.prototype.canCreateProjects = function() {
    return this.isActive() && this.isVerified();
  };

  Organization.prototype.calculateTransactionFee = function(amount) {
    return amount * this.transactionFeeRate;
  };

  Organization.prototype.calculateListingFee = function(amount) {
    return amount * this.listingFeeRate;
  };

  // Class methods
  Organization.findActiveOrganizations = function() {
    return this.findAll({
      where: {
        status: 'ACTIVE'
      }
    });
  };

  Organization.findVerifiedOrganizations = function() {
    return this.findAll({
      where: {
        verificationStatus: 'VERIFIED'
      }
    });
  };

  // Associations
  Organization.associate = function(models) {
    // Organization has many users
    Organization.hasMany(models.Auth, {
      foreignKey: 'organizationId',
      as: 'users'
    });

    // Organization has many projects
    if (models.Project) {
      Organization.hasMany(models.Project, {
        foreignKey: 'organizationId',
        as: 'projects'
      });
    }

    // Organization has many carbon credits
    if (models.CarbonCredit) {
      Organization.hasMany(models.CarbonCredit, {
        foreignKey: 'organizationId',
        as: 'carbonCredits'
      });
    }

    // Organization has many orders as buyer
    if (models.Order) {
      Organization.hasMany(models.Order, {
        foreignKey: 'buyerOrganizationId',
        as: 'purchaseOrders'
      });

      // Organization has many orders as seller
      Organization.hasMany(models.Order, {
        foreignKey: 'sellerOrganizationId',
        as: 'salesOrders'
      });
    }

    // Organization has many wallets
    if (models.Wallet) {
      Organization.hasMany(models.Wallet, {
        foreignKey: 'organizationId',
        as: 'wallets'
      });
    }

    // Organization has many notifications
    if (models.Notification) {
      Organization.hasMany(models.Notification, {
        foreignKey: 'organizationId',
        as: 'notifications'
      });
    }

    // Organization has many audit logs
    if (models.AuditLog) {
      Organization.hasMany(models.AuditLog, {
        foreignKey: 'organizationId',
        as: 'auditLogs'
      });
    }
  };

  return Organization;
};
