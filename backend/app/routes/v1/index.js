const app = require('express')();
const { verifyApi<PERSON>ey } = require('../../middleware/auth');

const swaggerroutesV1 = require('./swagger/index');
app.use('/', swaggerroutesV1);
app.use(verifyApiKey);

// Authentication routes
app.use('/auth', require('./auth'));

// Carbon credit routes (temporarily disabled)
// app.use('/carbon-credits', require('./carbonCredits'));

// Health check route
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  });
});

module.exports = app;
