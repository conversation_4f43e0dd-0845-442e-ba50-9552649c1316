const response = require('../response');
const authJwt = require('../middleware');
const httpStatus = require('http-status');
const passwordHash = require('../utils/password');
const helper = require('../utils/helper');
const db = require('../models/index').sequelize;
const { env } = require('../constant/environment');
const { USER_STATUS } = require('../constant/auth');
const commonService = require('../services/common');
const { Op } = require('sequelize');
const { withTransaction, withRetryTransaction } = require('../utils/transaction');
const logger = require('../utils/logger');

exports.login = async (req, res, next) => {
  try {
    const { email, device_id, device_token, device_type } = req.body;

    // Use advanced transaction with retry logic
    const result = await withRetryTransaction(async (transaction) => {
      const { Auth, Session, AuditLog } = db.models;
      const condition = { email: email.toLowerCase() };

      // Find user with organization details
      const checkUser = await commonService.findByCondition(Auth, condition, {
        include: [
          {
            model: db.models.Organization,
            as: 'organization',
            attributes: ['id', 'name', 'status', 'verificationStatus']
          }
        ],
        transaction
      });

      if (!checkUser) {
        // Log failed login attempt
        await AuditLog.logEvent(
          'LOGIN_FAILED',
          `Failed login attempt: ${email} (user not found)`,
          { email },
          null,
          null,
          req.ip,
          req.get('User-Agent'),
          { severity: 'MEDIUM', category: 'SECURITY' }
        );
        throw new Error('INVALID_CREDENTIALS');
      }

      // Validate password using the model method
      const isLogin = await checkUser.validatePassword(req.body.password);
      if (!isLogin) {
        // Log failed login attempt
        await AuditLog.logEvent(
          'LOGIN_FAILED',
          `Failed login attempt: ${email} (invalid password)`,
          { userId: checkUser.id },
          checkUser.id,
          checkUser.organizationId,
          req.ip,
          req.get('User-Agent'),
          { severity: 'HIGH', category: 'SECURITY' }
        );
        throw new Error('INVALID_CREDENTIALS');
      }

      // Check if user is active
      if (!checkUser.isActive()) {
        throw new Error('ACCOUNT_SUSPENDED');
      }

      // Check device limit
      const totalLogin = await commonService.count(Session, { auth_id: checkUser.id }, { transaction });
      if (totalLogin > (env.MAX_LOGIN_DEVICE * 1)) {
        throw new Error('TOTAL_LOGIN');
      }

      // Update last login
      await checkUser.updateLastLogin({ transaction });

      // Generate JWT token
      const token = authJwt.generateAuthJwt({
        id: checkUser.id,
        role: checkUser.role,
        organizationId: checkUser.organizationId,
        expires_in: env.TOKEN_EXPIRES_IN,
        email,
        device_id
      });

      if (!token) {
        throw new Error('TOKEN_GENERATION_FAILED');
      }

      // Log successful login
      await AuditLog.logEvent(
        'LOGIN_SUCCESS',
        `User logged in: ${email}`,
        {
          userId: checkUser.id,
          deviceId: device_id,
          deviceType: device_type
        },
        checkUser.id,
        checkUser.organizationId,
        req.ip,
        req.get('User-Agent'),
        { severity: 'LOW', category: 'AUTHENTICATION' }
      );

      // Prepare user data (excluding sensitive fields)
      const { password, ...userData } = checkUser.toJSON();
      userData.token = token;

      return {
        userData,
        deviceDetails: { device_id, device_token, device_type }
      };
    });

    // Pass data to next middleware
    req.loginData = {
      device_details: result.deviceDetails,
      auth_details: result.userData
    };

    logger.auth.login(email, true);
    return next();

  } catch (err) {
    logger.auth.login(req.body.email, false);
    logger.error('Login error:', err);

    // Map specific errors to response codes
    const errorMap = {
      'INVALID_CREDENTIALS': 'INVALID_CREDENTIALS',
      'ACCOUNT_SUSPENDED': 'ACCOUNT_SUSPENDED',
      'TOTAL_LOGIN': 'TOTAL_LOGIN',
      'TOKEN_GENERATION_FAILED': 'INTERNAL_SERVER_ERROR'
    };

    const msgCode = errorMap[err.message] || 'INTERNAL_SERVER_ERROR';
    const statusCode = msgCode === 'INVALID_CREDENTIALS' ? httpStatus.UNAUTHORIZED : httpStatus.INTERNAL_SERVER_ERROR;

    return response.error(req, res, { msgCode }, statusCode);
  }
};

exports.createSession = async (req, res) => {
  const { dbTrans } = req.loginData;
  try {
    const { device_id, device_token, device_type } = req.loginData.device_details;
    const condition = { device_id };
    const { Session } = await db.models;
    const checkSession = await commonService.findByCondition(Session, condition);

    if (checkSession) {
      const condition = { id: checkSession.id };
      // for hard delete true is required to pass in delete query
      const destroySession = await commonService.deleteQuery(Session, condition, dbTrans, true);
      if (!destroySession) {
        return response.error(req, res, { msgCode: helper.getErrorMsgCode(req) }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
      }
    }
    const sessionData = {
      id: helper.genUUID(),
      auth_id: req.loginData.auth_details.id,
      device_id,
      device_token,
      device_type,
      jwt_token: req.loginData.auth_details.token
    };
    const createSession = await commonService.addDetail(Session, sessionData, dbTrans);
    if (!createSession) {
      return response.error(req, res, { msgCode: helper.getErrorMsgCode(req) }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }

    const { ...data } = req.loginData.auth_details;

    const msgCode = helper.getSuccessMsgCode(req);
    return response.success(req, res, { msgCode, data }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.sendOtp = async (req, res) => {
  const dbTrans = await db.transaction();

  try {
    const { Otp } = db.models;
    const { phone_no } = req.body;
    const otp = helper.generateOtp(env.OTP_DIGIT);
    const hashOtp = await passwordHash.generateHash(otp);
    const otpData = {
      id: helper.genUUID(),
      user: phone_no,
      otp: hashOtp
    };
    const condition = { user: phone_no };
    const token = authJwt.generateAuthJwt({
      phone_no,
      expires_in: env.OTP_EXPIRES_IN
    });
    const checkOtp = await commonService.findByCondition(Otp, condition);
    if (checkOtp) {
      // if condition match than we update otp in existing row
      const updateData = await commonService.updateData(Otp, { otp: hashOtp }, condition, dbTrans);
      if (!updateData) {
        return response.error(req, res, { msgCode: 'OTP_NOT_SEND' }, httpStatus.FORBIDDEN, dbTrans);
      }
      return response.success(req, res, { msgCode: 'OTP_SENT', data: { token, OTP: otp } }, httpStatus.OK, dbTrans);
    }
    const createOtpDetails = await commonService.addDetail(Otp, otpData, dbTrans);
    if (!createOtpDetails) {
      return response.error(req, res, { msgCode: 'OTP_NOT_SEND' }, httpStatus.FORBIDDEN, dbTrans);
    }
    return response.success(req, res, { msgCode: 'OTP_SENT', data: { token, OTP: otp } }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.verifyOtp = async (req, res, next) => {
  const dbTrans = await db.transaction();
  try {
    const { Otp } = await db.models;
    const { phone_no, otp } = req.body;
    const condition = { user: phone_no };

    // get data from token
    const { ...tokenData } = req.token;
    if (tokenData.phone_no !== phone_no) {
      return response.error(req, res, { msgCode: 'INVALID_TOKEN' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const details = await commonService.findByCondition(Otp, condition);
    // console.log('details', details);

    if (!details) {
      return response.error(req, res, { msgCode: 'OTP_EXPIRED' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const check = passwordHash.comparePassword(otp, details.otp);
    if (!check) {
      return response.error(req, res, { msgCode: 'INVALID_OTP' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const token = authJwt.generateAuthJwt({
      phone_no,
      is_verified: true,
      expires_in: env.OTP_EXPIRES_IN
    });

    if (!token) {
      return response.error(req, res, { msgCode: 'EMAIL_v_FAILED' }, httpStatus.FORBIDDEN, dbTrans);
    }
    const deleteOtp = await commonService.deleteQuery(Otp, condition, dbTrans, true);
    if (!deleteOtp) {
      return response.error(req, res, { msgCode: 'EMAIL_v_FAILED' }, httpStatus.FORBIDDEN, dbTrans);
    }

    // you can remove 242 to 248 according your requirement because i also use it in case of forgot- password

    if (req.headers.authorization) {
      req.verifyData = {
        phone_no,
        dbTrans
      };
      return next();
    }
    const data = {
      Token: token
    };
    return response.success(req, res, { msgCode: 'EMAIL_VERIFIED', data }, httpStatus.ACCEPTED, dbTrans);
  } catch (error) {
    console.log(error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.resetPassword = async (req, res) => {
  const dbTrans = await db.transaction();
  try {
    const { Auth } = await db.models;
    const { new_password } = req.body;
    const { ...tokenData } = req.token;
    if (!tokenData.is_verified) {
      return response.error(req, res, { msgCode: 'INVALID_TOKEN' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const updateCondition = { email: tokenData.email };
    const hashPassword = await passwordHash.generateHash(new_password);
    const data = { password: hashPassword };
    const updateUser = await commonService.updateData(Auth, data, updateCondition);
    if (!updateUser) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.FORBIDDEN, dbTrans);
    }
    return response.success(req, res, { msgCode: 'PASSWORD_UPDATED' }, httpStatus.CREATED, dbTrans);
  } catch (error) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

// this function is used for check email is exist or not if exist it returns already registered

exports.isEmailExist = async (req, res, next) => {
  try {
    const { Auth } = db.models;

    const { email } = req.body;
    const condition = { email: email.toLowerCase() };
    const checkUserExist = await commonService.findByCondition(Auth, condition);
    if (!checkUserExist) {
      return next();
    }
    return response.error(req, res, { msgCode: 'ALREADY_REGISTERED' }, httpStatus.CONFLICT);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

// this function is used for check phone no is exist or not if exist it returns already registered

exports.isPhoneExist = async (req, res, next) => {
  try {
    const { Auth } = db.models;
    const { country_code, phone_no } = req.body;
    const condition = { country_code, phone_no };
    const checkPhone = await commonService.findByCondition(Auth, condition);
    if (!checkPhone) { return next(); }
    return response.error(req, res, { msgCode: 'ALREADY_REGISTERED' }, httpStatus.CONFLICT);
  } catch (error) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

// this function is used for check email is exist or not if not it return unauthorized

exports.isUserExist = async (req, res, next) => {
  try {
    const { Auth } = db.models;
    const { email } = req.body;
    const condition = { email: email.toLowerCase() };
    const checkUserExist = await commonService.findByCondition(Auth, condition);
    if (!checkUserExist) {
      return response.error(req, res, { msgCode: 'UNAUTHORIZED' }, httpStatus.UNAUTHORIZED);
    }
    return next();
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

exports.changePassword = async (req, res) => {
  const dbTrans = await db.transaction();

  try {
    const { ...tokenData } = req.data;
    // Below we require model
    const { Auth, Session } = db.models;

    const { new_password, old_password, logout } = req.body;
    const condition = { id: tokenData.id };
    const userDetails = await commonService.findByCondition(Auth, condition);
    if (!userDetails) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.FORBIDDEN, dbTrans);
    }
    // check old password is correct or not
    const check = passwordHash.comparePassword(old_password, userDetails.password);
    if (!check) {
      return response.error(req, res, { msgCode: 'WRONG_PASS' }, httpStatus.UNAUTHORIZED, dbTrans);
    }
    const hashPassword = await passwordHash.generateHash(new_password);
    const data = {
      password: hashPassword
    };
    const updateUser = await commonService.updateData(Auth, data, condition);
    if (!updateUser) {
      return response.error(req, res, { msgCode: 'UPDATE_ERROR' }, httpStatus.FORBIDDEN, dbTrans);
    }

    // if user want to logout all other device than pass logout true

    if (logout) {
      const sessionCondition = {
        [Op.and]: [{ auth_id: tokenData.id }, { device_id: { [Op.ne]: tokenData.device_id } }]
      };
      await commonService.deleteQuery(Session, sessionCondition, dbTrans, true);
    }
    return response.success(req, res, { msgCode: 'PASSWORD_UPDATED' }, httpStatus.OK, dbTrans);
  } catch (error) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};

exports.logout = async (req, res) => {
  const dbTrans = await db.transaction(); // Creating database transaction
  try {
    // auth id we get from token
    const condition = {
      auth_id: req.data.id,
      device_id: req.data.device_id
    };
    const { Session } = await db.models;
    const destroySession = await commonService.deleteQuery(Session, condition, dbTrans, true);
    if (!destroySession) {
      return response.error(req, res, { msgCode: 'USER_NOT_FOUND' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
    }
    return response.success(req, res, { msgCode: 'LOGOUT_SUCCESSFUL', data: null }, httpStatus.OK, dbTrans);
  } catch (err) {
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR, dbTrans);
  }
};
