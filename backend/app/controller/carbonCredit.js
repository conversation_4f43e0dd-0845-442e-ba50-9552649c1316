const response = require('../response');
const httpStatus = require('http-status');
const { withTransaction, withRetryTransaction, withBatchTransaction } = require('../utils/transaction');
const logger = require('../utils/logger');
const db = require('../models/index');
const { Op } = require('sequelize');
const Joi = require('joi');

// Validation schemas
const createCarbonCreditSchema = Joi.object({
  name: Joi.string().min(2).max(200).required(),
  description: Joi.string().max(1000).optional(),
  quantity: Joi.number().positive().required(),
  price: Joi.number().positive().required(),
  vintage: Joi.number().integer().min(1990).max(new Date().getFullYear() + 10).required(),
  standard: Joi.string().valid('VCS', 'CDM', 'GOLD_STANDARD', 'CAR', 'ACR', 'OTHER').required(),
  methodology: Joi.string().required(),
  projectId: Joi.string().required(),
  minPurchaseQuantity: Joi.number().positive().optional(),
  maxPurchaseQuantity: Joi.number().positive().optional()
});

const purchaseCarbonCreditSchema = Joi.object({
  quantity: Joi.number().positive().required(),
  paymentMethod: Joi.string().valid('CREDIT_CARD', 'BANK_TRANSFER', 'CRYPTO', 'WALLET', 'OTHER').optional()
});

/**
 * Get carbon credits with filtering and pagination
 */
exports.getCarbonCredits = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      vintage, 
      standard, 
      minPrice, 
      maxPrice,
      projectId,
      organizationId 
    } = req.query;
    
    // Build where clause
    const where = {};
    if (status) where.status = status;
    if (vintage) where.vintage = vintage;
    if (standard) where.standard = standard;
    if (projectId) where.projectId = projectId;
    if (organizationId) where.organizationId = organizationId;
    
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price[Op.gte] = parseFloat(minPrice);
      if (maxPrice) where.price[Op.lte] = parseFloat(maxPrice);
    }

    const offset = (page - 1) * limit;

    const { CarbonCredit, Project, Organization, Auth } = db.models;
    const { rows: carbonCredits, count } = await CarbonCredit.findAndCountAll({
      where,
      include: [
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'type', 'location', 'country']
        },
        {
          model: Organization,
          as: 'organization',
          attributes: ['id', 'name']
        },
        {
          model: Auth,
          as: 'user',
          attributes: ['id', 'name', 'email']
        }
      ],
      limit: parseInt(limit),
      offset,
      order: [['createdAt', 'DESC']]
    });

    const data = {
      carbonCredits,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    };

    return response.success(req, res, { msgCode: 'SUCCESS', data }, httpStatus.OK);

  } catch (error) {
    logger.error('Get carbon credits error:', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

/**
 * Create a new carbon credit
 */
exports.createCarbonCredit = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = createCarbonCreditSchema.validate(req.body);
    if (error) {
      return response.error(req, res, { 
        msgCode: 'VALIDATION_ERROR', 
        details: error.details 
      }, httpStatus.BAD_REQUEST);
    }

    const result = await withTransaction(async (transaction) => {
      const { CarbonCredit, Project, AuditLog } = db.models;

      // Verify project exists and belongs to user's organization
      const project = await Project.findOne({
        where: {
          id: value.projectId,
          organizationId: req.user.organizationId
        },
        transaction
      });

      if (!project) {
        throw new Error('PROJECT_NOT_FOUND');
      }

      if (!project.canIssueCredits()) {
        throw new Error('PROJECT_NOT_VERIFIED');
      }

      // Create carbon credit
      const carbonCredit = await CarbonCredit.create({
        ...value,
        availableQuantity: value.quantity,
        userId: req.user.id,
        organizationId: req.user.organizationId,
        status: 'PENDING',
        verificationStatus: 'PENDING',
        listingDate: new Date()
      }, { transaction });

      // Generate serial number
      await carbonCredit.generateSerialNumber({ transaction });

      // Log creation
      await AuditLog.logEvent(
        'CREDIT_CREATED',
        `Carbon credit created: ${carbonCredit.name}`,
        { 
          carbonCreditId: carbonCredit.id,
          projectId: project.id,
          quantity: carbonCredit.quantity,
          price: carbonCredit.price
        },
        req.user.id,
        req.user.organizationId,
        req.ip,
        req.get('User-Agent'),
        { severity: 'LOW', category: 'BUSINESS' }
      );

      return { carbonCredit, project };
    });

    logger.info(`Carbon credit created: ${result.carbonCredit.name} by user ${req.user.email}`);

    return response.success(req, res, {
      msgCode: 'CREDIT_CREATED_SUCCESS',
      data: result.carbonCredit
    }, httpStatus.CREATED);

  } catch (error) {
    logger.error('Create carbon credit error:', error);

    const errorMap = {
      'PROJECT_NOT_FOUND': 'PROJECT_NOT_FOUND',
      'PROJECT_NOT_VERIFIED': 'PROJECT_NOT_VERIFIED'
    };

    const msgCode = errorMap[error.message] || 'INTERNAL_SERVER_ERROR';
    const statusCode = msgCode === 'INTERNAL_SERVER_ERROR' ? httpStatus.INTERNAL_SERVER_ERROR : httpStatus.BAD_REQUEST;

    return response.error(req, res, { msgCode }, statusCode);
  }
};

/**
 * Purchase carbon credits (atomic transaction with row-level locking)
 */
exports.purchaseCarbonCredit = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = purchaseCarbonCreditSchema.validate(req.body);
    if (error) {
      return response.error(req, res, { 
        msgCode: 'VALIDATION_ERROR', 
        details: error.details 
      }, httpStatus.BAD_REQUEST);
    }

    const { quantity, paymentMethod = 'CREDIT_CARD' } = value;
    const carbonCreditId = req.params.id;

    // Use retry transaction for purchase (handles concurrent purchases)
    const result = await withRetryTransaction(async (transaction) => {
      const { CarbonCredit, Order, Auth, Organization, AuditLog } = db.models;

      // Get carbon credit with lock to prevent race conditions
      const carbonCredit = await CarbonCredit.findByPk(carbonCreditId, {
        include: [
          {
            model: Auth,
            as: 'user',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Organization,
            as: 'organization',
            attributes: ['id', 'name', 'transactionFeeRate']
          }
        ],
        lock: transaction.LOCK.UPDATE, // Row-level lock
        transaction
      });

      if (!carbonCredit) {
        throw new Error('CREDIT_NOT_FOUND');
      }

      // Validate purchase
      if (!carbonCredit.canPurchase(quantity)) {
        throw new Error('INSUFFICIENT_QUANTITY');
      }

      if (carbonCredit.userId === req.user.id) {
        throw new Error('CANNOT_PURCHASE_OWN_CREDIT');
      }

      // Calculate fees
      const subtotal = carbonCredit.calculateTotalPrice(quantity);
      const transactionFee = carbonCredit.organization.transactionFeeRate * subtotal;
      const platformFee = subtotal * 0.005; // 0.5% platform fee

      // Create order
      const order = await Order.create({
        type: 'BUY',
        quantity,
        price: carbonCredit.price,
        totalAmount: subtotal + transactionFee + platformFee,
        transactionFee,
        platformFee,
        paymentMethod,
        carbonCreditId: carbonCredit.id,
        buyerId: req.user.id,
        sellerId: carbonCredit.userId,
        buyerOrganizationId: req.user.organizationId,
        sellerOrganizationId: carbonCredit.organizationId,
        status: 'COMPLETED', // Simplified - in real app, this would be PENDING until payment
        paymentStatus: 'COMPLETED'
      }, { transaction });

      // Update carbon credit availability
      await carbonCredit.updateAvailableQuantity(quantity, { transaction });

      // Log purchase
      await AuditLog.logEvent(
        'CREDIT_PURCHASED',
        `Carbon credit purchased: ${quantity} units of ${carbonCredit.name}`,
        {
          orderId: order.id,
          carbonCreditId: carbonCredit.id,
          quantity,
          price: carbonCredit.price,
          totalAmount: order.totalAmount,
          sellerId: carbonCredit.userId
        },
        req.user.id,
        req.user.organizationId,
        req.ip,
        req.get('User-Agent'),
        { severity: 'LOW', category: 'BUSINESS' }
      );

      return { order, carbonCredit };
    }, {
      maxRetries: 3, // Retry up to 3 times for deadlocks
      baseDelay: 100 // Start with 100ms delay
    });

    logger.info(`Carbon credit purchased: ${quantity} units by user ${req.user.email}`);

    return response.success(req, res, {
      msgCode: 'PURCHASE_SUCCESS',
      data: {
        order: result.order,
        carbonCredit: {
          id: result.carbonCredit.id,
          name: result.carbonCredit.name,
          availableQuantity: result.carbonCredit.availableQuantity
        }
      }
    }, httpStatus.OK);

  } catch (error) {
    logger.error('Purchase carbon credit error:', error);

    const errorMap = {
      'CREDIT_NOT_FOUND': 'CREDIT_NOT_FOUND',
      'INSUFFICIENT_QUANTITY': 'INSUFFICIENT_QUANTITY',
      'CANNOT_PURCHASE_OWN_CREDIT': 'CANNOT_PURCHASE_OWN_CREDIT'
    };

    const msgCode = errorMap[error.message] || 'INTERNAL_SERVER_ERROR';
    const statusCode = msgCode === 'INTERNAL_SERVER_ERROR' ? httpStatus.INTERNAL_SERVER_ERROR : httpStatus.BAD_REQUEST;

    return response.error(req, res, { msgCode }, statusCode);
  }
};

/**
 * Retire multiple carbon credits in a single transaction
 */
exports.retireCarbonCredits = async (req, res) => {
  try {
    const { carbonCreditIds, retirementReason, retirementBeneficiary } = req.body;

    if (!Array.isArray(carbonCreditIds) || carbonCreditIds.length === 0) {
      return response.error(req, res, { 
        msgCode: 'VALIDATION_ERROR',
        details: 'carbonCreditIds must be a non-empty array'
      }, httpStatus.BAD_REQUEST);
    }

    // Use batch transaction for retiring multiple credits
    const operations = carbonCreditIds.map(creditId => async (transaction) => {
      const { CarbonCredit, AuditLog } = db.models;

      const carbonCredit = await CarbonCredit.findOne({
        where: {
          id: creditId,
          userId: req.user.id // Only allow retiring own credits
        },
        transaction
      });

      if (!carbonCredit) {
        throw new Error(`CREDIT_NOT_FOUND_${creditId}`);
      }

      if (carbonCredit.isRetired()) {
        throw new Error(`CREDIT_ALREADY_RETIRED_${creditId}`);
      }

      // Retire the credit
      await carbonCredit.retire(retirementReason, retirementBeneficiary, { transaction });

      // Log retirement
      await AuditLog.logEvent(
        'CREDIT_RETIRED',
        `Carbon credit retired: ${carbonCredit.name}`,
        {
          carbonCreditId: carbonCredit.id,
          retirementReason,
          retirementBeneficiary,
          quantity: carbonCredit.quantity
        },
        req.user.id,
        req.user.organizationId,
        req.ip,
        req.get('User-Agent'),
        { severity: 'LOW', category: 'BUSINESS' }
      );

      return carbonCredit;
    });

    const retiredCredits = await withBatchTransaction(operations);

    logger.info(`Batch retirement completed: ${retiredCredits.length} credits by user ${req.user.email}`);

    return response.success(req, res, {
      msgCode: 'RETIREMENT_SUCCESS',
      data: {
        retiredCredits: retiredCredits.map(credit => ({
          id: credit.id,
          name: credit.name,
          status: credit.status,
          retirementDate: credit.retirementDate,
          quantity: credit.quantity
        }))
      }
    }, httpStatus.OK);

  } catch (error) {
    logger.error('Retire carbon credits error:', error);
    return response.error(req, res, { msgCode: 'INTERNAL_SERVER_ERROR' }, httpStatus.INTERNAL_SERVER_ERROR);
  }
};

module.exports = exports;
