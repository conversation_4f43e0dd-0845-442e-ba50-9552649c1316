// Generated by dts-bundle v0.7.3

declare module '@eslint-community/regexpp' {
    import * as AST from "@eslint-community/regexpp/ast";
    import { RegExpParser } from "@eslint-community/regexpp/parser";
    import { RegExpValidator } from "@eslint-community/regexpp/validator";
    import { RegExpVisitor } from "@eslint-community/regexpp/visitor";
    export { AST, RegExpParser, RegExpValidator };
    export function parseRegExpLiteral(source: RegExp | string, options?: RegExpParser.Options): AST.RegExpLiteral;
    export function validateRegExpLiteral(source: string, options?: RegExpValidator.Options): void;
    export function visitRegExpAST(node: AST.Node, handlers: RegExpVisitor.Handlers): void;
}

declare module '@eslint-community/regexpp/ast' {
    export type Node = BranchNode | LeafNode;
    export type BranchNode = Alternative | CapturingGroup | CharacterClass | CharacterClassRange | Group | LookaroundAssertion | Pattern | Quantifier | RegExpLiteral;
    export type LeafNode = Backreference | BoundaryAssertion | Character | CharacterSet | Flags;
    export type Element = Assertion | QuantifiableElement | Quantifier;
    export type QuantifiableElement = Backreference | CapturingGroup | Character | CharacterClass | CharacterSet | Group | LookaheadAssertion;
    export type CharacterClassElement = Character | CharacterClassRange | EscapeCharacterSet | UnicodePropertyCharacterSet;
    export interface NodeBase {
        type: Node["type"];
        parent: Node["parent"];
        start: number;
        end: number;
        raw: string;
    }
    export interface RegExpLiteral extends NodeBase {
        type: "RegExpLiteral";
        parent: null;
        pattern: Pattern;
        flags: Flags;
    }
    export interface Pattern extends NodeBase {
        type: "Pattern";
        parent: RegExpLiteral | null;
        alternatives: Alternative[];
    }
    export interface Alternative extends NodeBase {
        type: "Alternative";
        parent: CapturingGroup | Group | LookaroundAssertion | Pattern;
        elements: Element[];
    }
    export interface Group extends NodeBase {
        type: "Group";
        parent: Alternative | Quantifier;
        alternatives: Alternative[];
    }
    export interface CapturingGroup extends NodeBase {
        type: "CapturingGroup";
        parent: Alternative | Quantifier;
        name: string | null;
        alternatives: Alternative[];
        references: Backreference[];
    }
    export type LookaroundAssertion = LookaheadAssertion | LookbehindAssertion;
    export interface LookaheadAssertion extends NodeBase {
        type: "Assertion";
        parent: Alternative | Quantifier;
        kind: "lookahead";
        negate: boolean;
        alternatives: Alternative[];
    }
    export interface LookbehindAssertion extends NodeBase {
        type: "Assertion";
        parent: Alternative;
        kind: "lookbehind";
        negate: boolean;
        alternatives: Alternative[];
    }
    export interface Quantifier extends NodeBase {
        type: "Quantifier";
        parent: Alternative;
        min: number;
        max: number;
        greedy: boolean;
        element: QuantifiableElement;
    }
    export interface CharacterClass extends NodeBase {
        type: "CharacterClass";
        parent: Alternative | Quantifier;
        negate: boolean;
        elements: CharacterClassElement[];
    }
    export interface CharacterClassRange extends NodeBase {
        type: "CharacterClassRange";
        parent: CharacterClass;
        min: Character;
        max: Character;
    }
    export type Assertion = BoundaryAssertion | LookaroundAssertion;
    export type BoundaryAssertion = EdgeAssertion | WordBoundaryAssertion;
    export interface EdgeAssertion extends NodeBase {
        type: "Assertion";
        parent: Alternative | Quantifier;
        kind: "end" | "start";
    }
    export interface WordBoundaryAssertion extends NodeBase {
        type: "Assertion";
        parent: Alternative | Quantifier;
        kind: "word";
        negate: boolean;
    }
    export type CharacterSet = AnyCharacterSet | EscapeCharacterSet | UnicodePropertyCharacterSet;
    export interface AnyCharacterSet extends NodeBase {
        type: "CharacterSet";
        parent: Alternative | Quantifier;
        kind: "any";
    }
    export interface EscapeCharacterSet extends NodeBase {
        type: "CharacterSet";
        parent: Alternative | CharacterClass | Quantifier;
        kind: "digit" | "space" | "word";
        negate: boolean;
    }
    export interface UnicodePropertyCharacterSet extends NodeBase {
        type: "CharacterSet";
        parent: Alternative | CharacterClass | Quantifier;
        kind: "property";
        key: string;
        value: string | null;
        negate: boolean;
    }
    export interface Character extends NodeBase {
        type: "Character";
        parent: Alternative | CharacterClass | CharacterClassRange | Quantifier;
        value: number;
    }
    export interface Backreference extends NodeBase {
        type: "Backreference";
        parent: Alternative | Quantifier;
        ref: number | string;
        resolved: CapturingGroup;
    }
    export interface Flags extends NodeBase {
        type: "Flags";
        parent: RegExpLiteral | null;
        dotAll: boolean;
        global: boolean;
        hasIndices: boolean;
        ignoreCase: boolean;
        multiline: boolean;
        sticky: boolean;
        unicode: boolean;
    }
}

declare module '@eslint-community/regexpp/parser' {
    import type { Flags, RegExpLiteral, Pattern } from "@eslint-community/regexpp/ast";
    import type { EcmaVersion } from "@eslint-community/regexpp/ecma-versions";
    export namespace RegExpParser {
        interface Options {
            strict?: boolean;
            ecmaVersion?: EcmaVersion;
        }
    }
    export class RegExpParser {
        constructor(options?: RegExpParser.Options);
        parseLiteral(source: string, start?: number, end?: number): RegExpLiteral;
        parseFlags(source: string, start?: number, end?: number): Flags;
        parsePattern(source: string, start?: number, end?: number, uFlag?: boolean): Pattern;
    }
}

declare module '@eslint-community/regexpp/validator' {
    import type { EcmaVersion } from "@eslint-community/regexpp/ecma-versions";
    export namespace RegExpValidator {
        interface Options {
            strict?: boolean;
            ecmaVersion?: EcmaVersion;
            onLiteralEnter?: (start: number) => void;
            onLiteralLeave?: (start: number, end: number) => void;
            onRegExpFlags?: (start: number, end: number, flags: {
                global: boolean;
                ignoreCase: boolean;
                multiline: boolean;
                unicode: boolean;
                sticky: boolean;
                dotAll: boolean;
                hasIndices: boolean;
            }) => void;
            onFlags?: (start: number, end: number, global: boolean, ignoreCase: boolean, multiline: boolean, unicode: boolean, sticky: boolean, dotAll: boolean, hasIndices: boolean) => void;
            onPatternEnter?: (start: number) => void;
            onPatternLeave?: (start: number, end: number) => void;
            onDisjunctionEnter?: (start: number) => void;
            onDisjunctionLeave?: (start: number, end: number) => void;
            onAlternativeEnter?: (start: number, index: number) => void;
            onAlternativeLeave?: (start: number, end: number, index: number) => void;
            onGroupEnter?: (start: number) => void;
            onGroupLeave?: (start: number, end: number) => void;
            onCapturingGroupEnter?: (start: number, name: string | null) => void;
            onCapturingGroupLeave?: (start: number, end: number, name: string | null) => void;
            onQuantifier?: (start: number, end: number, min: number, max: number, greedy: boolean) => void;
            onLookaroundAssertionEnter?: (start: number, kind: "lookahead" | "lookbehind", negate: boolean) => void;
            onLookaroundAssertionLeave?: (start: number, end: number, kind: "lookahead" | "lookbehind", negate: boolean) => void;
            onEdgeAssertion?: (start: number, end: number, kind: "end" | "start") => void;
            onWordBoundaryAssertion?: (start: number, end: number, kind: "word", negate: boolean) => void;
            onAnyCharacterSet?: (start: number, end: number, kind: "any") => void;
            onEscapeCharacterSet?: (start: number, end: number, kind: "digit" | "space" | "word", negate: boolean) => void;
            onUnicodePropertyCharacterSet?: (start: number, end: number, kind: "property", key: string, value: string | null, negate: boolean) => void;
            onCharacter?: (start: number, end: number, value: number) => void;
            onBackreference?: (start: number, end: number, ref: number | string) => void;
            onCharacterClassEnter?: (start: number, negate: boolean) => void;
            onCharacterClassLeave?: (start: number, end: number, negate: boolean) => void;
            onCharacterClassRange?: (start: number, end: number, min: number, max: number) => void;
        }
    }
    export class RegExpValidator {
        constructor(options?: RegExpValidator.Options);
        validateLiteral(source: string, start?: number, end?: number): void;
        validateFlags(source: string, start?: number, end?: number): void;
        validatePattern(source: string, start?: number, end?: number, uFlag?: boolean): void;
    }
}

declare module '@eslint-community/regexpp/visitor' {
    import type { Alternative, Assertion, Backreference, CapturingGroup, Character, CharacterClass, CharacterClassRange, CharacterSet, Flags, Group, Node, Pattern, Quantifier, RegExpLiteral } from "@eslint-community/regexpp/ast";
    export class RegExpVisitor {
        constructor(handlers: RegExpVisitor.Handlers);
        visit(node: Node): void;
    }
    export namespace RegExpVisitor {
        interface Handlers {
            onAlternativeEnter?: (node: Alternative) => void;
            onAlternativeLeave?: (node: Alternative) => void;
            onAssertionEnter?: (node: Assertion) => void;
            onAssertionLeave?: (node: Assertion) => void;
            onBackreferenceEnter?: (node: Backreference) => void;
            onBackreferenceLeave?: (node: Backreference) => void;
            onCapturingGroupEnter?: (node: CapturingGroup) => void;
            onCapturingGroupLeave?: (node: CapturingGroup) => void;
            onCharacterEnter?: (node: Character) => void;
            onCharacterLeave?: (node: Character) => void;
            onCharacterClassEnter?: (node: CharacterClass) => void;
            onCharacterClassLeave?: (node: CharacterClass) => void;
            onCharacterClassRangeEnter?: (node: CharacterClassRange) => void;
            onCharacterClassRangeLeave?: (node: CharacterClassRange) => void;
            onCharacterSetEnter?: (node: CharacterSet) => void;
            onCharacterSetLeave?: (node: CharacterSet) => void;
            onFlagsEnter?: (node: Flags) => void;
            onFlagsLeave?: (node: Flags) => void;
            onGroupEnter?: (node: Group) => void;
            onGroupLeave?: (node: Group) => void;
            onPatternEnter?: (node: Pattern) => void;
            onPatternLeave?: (node: Pattern) => void;
            onQuantifierEnter?: (node: Quantifier) => void;
            onQuantifierLeave?: (node: Quantifier) => void;
            onRegExpLiteralEnter?: (node: RegExpLiteral) => void;
            onRegExpLiteralLeave?: (node: RegExpLiteral) => void;
        }
    }
}

declare module '@eslint-community/regexpp/ecma-versions' {
    export type EcmaVersion = 5 | 2015 | 2016 | 2017 | 2018 | 2019 | 2020 | 2021 | 2022;
}

